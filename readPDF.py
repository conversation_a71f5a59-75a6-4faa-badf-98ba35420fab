#!/usr/bin/env python3
"""
PDF AcroForm field reading script
Specifically designed to extract comb fields with numberOfCells attribute
"""

import json
import sys
from typing import Dict, List, Any, Optional
import PyPDF2
from PyPDF2.generic import DictionaryObject, ArrayObject, IndirectObject


class PDFAcroFormReader:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.reader = None
        self.acroform = None
        self.file_handle = None

    def open_pdf(self) -> bool:
        """Open PDF file and get AcroForm"""
        try:
            # Keep file open
            self.file_handle = open(self.pdf_path, 'rb')
            self.reader = PyPDF2.PdfReader(self.file_handle)

            # Get AcroForm
            if '/AcroForm' in self.reader.trailer['/Root']:
                self.acroform = self.reader.trailer['/Root']['/AcroForm']
                return True
            else:
                print("AcroForm not found in PDF file")
                return False

        except Exception as e:
            print(f"Error opening PDF file: {e}")
            return False

    def close_pdf(self):
        """Close PDF file"""
        if hasattr(self, 'file_handle') and self.file_handle:
            self.file_handle.close()

    def resolve_reference(self, obj: Any) -> Any:
        """Resolve indirect reference objects"""
        if isinstance(obj, IndirectObject):
            return obj.get_object()
        return obj

    def extract_field_info(self, field_obj: DictionaryObject, parent_name: str = "") -> Dict[str, Any]:
        """Extract field information"""
        field_info = {
            'name': '',
            'full_name': '',
            'type': '',
            'flags': 0,
            'numberOfCells': None,
            'has_comb': False,
            'field_width': None,
            'field_height': None,
            'letterSpace': None,
            'raw_field': {}
        }

        # Parse field object
        field = self.resolve_reference(field_obj)
        if not isinstance(field, DictionaryObject):
            return field_info

        # Get field name
        field_name = ""
        if '/T' in field:
            field_name = str(field['/T'])

        # Build full name
        if parent_name:
            full_name = f"{parent_name}.{field_name}" if field_name else parent_name
        else:
            full_name = field_name

        field_info['name'] = field_name
        field_info['full_name'] = full_name

        # Get field type
        if '/FT' in field:
            field_info['type'] = str(field['/FT'])

        # Get field flags
        if '/Ff' in field:
            flags = field['/Ff']
            if isinstance(flags, int):
                field_info['flags'] = flags
                # Check if comb flag is set (bit 24, value 0x1000000)
                field_info['has_comb'] = bool(flags & 0x1000000)

        # Find numberOfCells attribute - check multiple possible locations and names
        # 1. Direct search for numberOfCells
        if '/numberOfCells' in field:
            field_info['numberOfCells'] = field['/numberOfCells']

        # 2. Search for MaxLen (for comb fields, MaxLen is usually numberOfCells)
        elif '/MaxLen' in field:
            field_info['numberOfCells'] = field['/MaxLen']

        # 3. Check all keys for possible numberOfCells related attributes
        else:
            for key in field.keys():
                key_str = str(key).lower()
                if ('numberofcells' in key_str or
                    'number_of_cells' in key_str or
                    'cellcount' in key_str or
                    'cells' in key_str):
                    field_info['numberOfCells'] = field[key]
                    break

        # 4. For fields with comb flag, if numberOfCells not found yet, check MaxLen again
        if field_info['has_comb'] and field_info['numberOfCells'] is None and '/MaxLen' in field:
            field_info['numberOfCells'] = field['/MaxLen']

        # 5. Extract field size information
        if '/Rect' in field:
            rect = field['/Rect']
            if hasattr(rect, '__iter__') and len(rect) >= 4:
                # PDF Rect format: [x1, y1, x2, y2]
                x1, y1, x2, y2 = float(rect[0]), float(rect[1]), float(rect[2]), float(rect[3])
                field_info['field_width'] = abs(x2 - x1)
                field_info['field_height'] = abs(y2 - y1)

                # 6. Calculate letter spacing (letterSpace)
                if field_info['numberOfCells'] and field_info['numberOfCells'] > 0:
                    field_info['letterSpace'] = self.calculate_letter_space(
                        field_info['field_width'],
                        field_info['numberOfCells']
                    )

        # Save raw field data for debugging
        field_info['raw_field'] = {str(k): str(v) for k, v in field.items()}

        return field_info

    def calculate_letter_space(self, field_width: float, number_of_cells: int) -> float:
        """
        Calculate letter spacing

        Args:
            field_width: Field width (PDF units, usually points)
            number_of_cells: Number of character cells

        Returns:
            letterSpace: Letter spacing (PDF units)
        """
        if number_of_cells <= 1:
            return 0.0

        # Basic calculation: field width divided by number of characters to get average space per character
        # Then subtract estimated character width to get letter spacing

        # Assume average character width is about 60% of field height (empirical value)
        # If no height information available, use field width estimation
        estimated_char_width = field_width / number_of_cells * 0.6

        # Calculate total space available for spacing
        total_char_width = estimated_char_width * number_of_cells
        available_space_for_spacing = field_width - total_char_width

        # Letter spacing = available spacing space / (number of characters - 1)
        # Subtract 1 because n characters have n-1 spaces between them
        if number_of_cells > 1:
            letter_space = available_space_for_spacing / (number_of_cells - 1)
        else:
            letter_space = 0.0

        # Ensure letter spacing is not negative
        letter_space = max(0.0, letter_space)

       
            return letter_space

    def process_field_tree(self, fields: Any, parent_name: str = "") -> List[Dict[str, Any]]:
        """Recursively process field tree"""
        all_fields = []

        if not fields:
            return all_fields

        # Parse field array
        fields_array = self.resolve_reference(fields)
        if not isinstance(fields_array, ArrayObject):
            return all_fields

        for field_ref in fields_array:
            field_obj = self.resolve_reference(field_ref)
            if not isinstance(field_obj, DictionaryObject):
                continue

            # Extract current field information
            field_info = self.extract_field_info(field_obj, parent_name)

            # Check if there are child fields
            if '/Kids' in field_obj:
                # This is a parent field, recursively process child fields
                child_fields = self.process_field_tree(field_obj['/Kids'], field_info['full_name'])
                all_fields.extend(child_fields)
            else:
                # This is a leaf field
                all_fields.append(field_info)

        return all_fields

    def find_all_fields_with_numberOfCells(self) -> Dict[str, Any]:
        """Find all fields containing numberOfCells attribute"""
        if not self.open_pdf():
            return {
                "error": "Unable to open PDF file or find AcroForm",
                "pdf_file": self.pdf_path,
                "total_fields_found": 0,
                "fields": []
            }

        result = {
            "pdf_file": self.pdf_path,
            "total_fields_found": 0,
            "fields_with_numberOfCells": 0,
            "fields": [],
            "all_fields_summary": []
        }

        try:
            # Get field array
            if '/Fields' not in self.acroform:
                result["error"] = "No fields found in AcroForm"
                return result

            # Process all fields
            all_fields = self.process_field_tree(self.acroform['/Fields'])
            result["total_fields_found"] = len(all_fields)

            # Filter fields containing numberOfCells attribute
            matching_fields = []
            for field in all_fields:
                # Add to summary
                result["all_fields_summary"].append({
                    "name": field['full_name'],
                    "type": field['type'],
                    "has_comb": field['has_comb'],
                    "numberOfCells": field['numberOfCells'],
                    "field_width": field['field_width'],
                    "field_height": field['field_height'],
                    "letterSpace": field['letterSpace']
                })

                # Check if contains numberOfCells attribute (not None)
                if field['numberOfCells'] is not None:
                    matching_fields.append(field)

            result["fields"] = matching_fields
            result["fields_with_numberOfCells"] = len(matching_fields)

        except Exception as e:
            result["error"] = f"Error processing fields: {e}"

        return result


def main():
    """Main function"""
    pdf_path = "fw9.pdf"

    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]

    print(f"Analyzing PDF file: {pdf_path}")
    print(f"Searching for all fields containing numberOfCells attribute...")

    reader = PDFAcroFormReader(pdf_path)
    try:
        result = reader.find_all_fields_with_numberOfCells()

        # Output results to JSON file
        output_file = "numberOfCells_fields.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    finally:
        reader.close_pdf()

    print(f"\nResults saved to: {output_file}")

    # Print summary
    print(f"\n=== Analysis Summary ===")
    print(f"PDF file: {result['pdf_file']}")
    print(f"Total fields: {result['total_fields_found']}")
    print(f"Fields with numberOfCells attribute: {result['fields_with_numberOfCells']}")

    if result.get('error'):
        print(f"Error: {result['error']}")

    if result['fields']:
        print(f"\n=== Fields with numberOfCells attribute ===")
        for field in result['fields']:
            print(f"Field name: {field['full_name']}")
            print(f"Type: {field['type']}")
            print(f"numberOfCells: {field['numberOfCells']}")
            print(f"Is comb field: {field['has_comb']}")
            print(f"Field flags: {field['flags']}")
            print(f"Field width: {field['field_width']:.2f} pt" if field['field_width'] else "Field width: Unknown")
            print(f"Field height: {field['field_height']:.2f} pt" if field['field_height'] else "Field height: Unknown")
            print(f"letterSpace: {field['letterSpace']:.2f} pt" if field['letterSpace'] is not None else "letterSpace: Not calculated")
            print("-" * 50)
    else:
        print("\nNo fields with numberOfCells attribute found")

    # Display summary information for all fields
    if result.get('all_fields_summary'):
        print(f"\n=== All Fields Summary ===")
        for field_summary in result['all_fields_summary']:
            if field_summary['numberOfCells'] is not None:
                letter_space_info = f", letterSpace: {field_summary['letterSpace']:.2f}pt" if field_summary['letterSpace'] is not None else ""
                print(f"✓ {field_summary['name']} (Type: {field_summary['type']}, numberOfCells: {field_summary['numberOfCells']}{letter_space_info})")
            else:
                print(f"  {field_summary['name']} (Type: {field_summary['type']}, No numberOfCells)")

    # Create letter spacing calculation summary table
    if result['fields']:
        print(f"\n=== Letter Spacing Calculation Summary Table ===")
        print(f"{'Field Name':<25} {'numberOfCells':<12} {'Field Width(pt)':<12} {'letterSpace':<15}")
        print("-" * 70)
        for field in result['fields']:
            field_name = field['name'] if len(field['name']) <= 24 else field['name'][:21] + "..."
            print(f"{field_name:<25} {field['numberOfCells']:<12} {field['field_width']:<12.2f} {field['letterSpace']:<15.2f}")

    print(f"\nFor detailed information, see: {output_file}")


if __name__ == "__main__":
    main()